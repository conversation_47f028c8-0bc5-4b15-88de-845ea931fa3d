<template>
  <HerbitProfessionalLayout
    title="Create Assessment"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Form Card -->
    <FormCard color="cyan">
          <form @submit.prevent="createAssessment" class="space-y-6">
              <!-- Assessment Name -->
              <div>
                <Label for="assessmentName" class="text-gray-300">Assessment Name</Label>
                <Input
                  id="assessmentName"
                  name="assessmentName"
                  v-model="assessmentName"
                  type="text"
                  autocomplete="off"
                  placeholder="e.g. DevOps Basics"
                  required
                />
              </div>

              <!-- Assessment Description -->
              <div>
                <Label for="assessmentDescription" class="text-gray-300">Assessment Description</Label>
                <textarea
                  id="assessmentDescription"
                  name="assessmentDescription"
                  v-model="assessmentDescription"
                  autocomplete="off"
                  placeholder="e.g. A comprehensive assessment of DevOps fundamentals including CI/CD pipelines, containerization, and infrastructure automation"
                  required
                  class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent resize-y min-h-[100px]"
                ></textarea>
                <div class="mt-1 text-xs text-gray-400">
                  Provide a detailed description of what this assessment covers (minimum 20 characters)
                </div>
              </div>

              <!-- Skill Selection (Multiple) -->
              <div>
                <Label class="text-gray-300">Select Skills</Label>
                <div class="mb-2 text-xs text-gray-400">You can select multiple skills for this assessment</div>
                <div class="relative skill-dropdown-container">
                  <button
                    @click="toggleSkillDropdown"
                    type="button"
                    class="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent flex justify-between items-center hover:bg-gray-700 transition-colors"
                  >
                    <span v-if="selectedSkillIds.length === 0" class="text-gray-400">Select skills...</span>
                    <span v-else class="text-white">{{ selectedSkillIds.length }} skill(s) selected</span>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>

                  <!-- Dropdown Menu -->
                  <div
                    v-if="showSkillDropdown"
                    class="absolute z-10 mt-1 w-full max-h-60 overflow-y-auto bg-gray-800 border border-gray-700 rounded-lg shadow-lg"
                  >
                    <div
                      v-for="skill in skills"
                      :key="skill.id"
                      class="flex items-center px-4 py-2 border-b border-gray-700 last:border-b-0 hover:bg-gray-700/50"
                    >
                      <input
                        type="checkbox"
                        :id="`skill-${skill.id}`"
                        :name="`skill-${skill.id}`"
                        :value="skill.id"
                        v-model="selectedSkillIds"
                        autocomplete="off"
                        class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800 rounded"
                        @change="onSkillSelectionChange"
                      />
                      <Label :for="`skill-${skill.id}`" class="ml-2 text-sm text-gray-300 cursor-pointer flex-1">
                        {{ skill.name }}
                      </Label>
                    </div>
                  </div>
                </div>

                <!-- Selected Skills Display -->
                <div v-if="selectedSkillIds.length > 0" class="mt-2 flex flex-wrap gap-2">
                  <div
                    v-for="skillId in selectedSkillIds"
                    :key="skillId"
                    class="inline-flex items-center bg-cyan-900/50 text-cyan-200 text-xs px-2 py-1 rounded-lg"
                  >
                    {{ getSkillName(skillId) }}
                    <button
                      @click="removeSkill(skillId)"
                      type="button"
                      class="ml-1 text-cyan-300 hover:text-white hover:bg-cyan-900/30 rounded px-1 transition-colors"
                    >
                      &times;
                    </button>
                  </div>
                </div>
                <div v-if="selectedSkillIds.length === 0" class="mt-2 text-xs text-red-400">
                  Please select at least one skill
                </div>
              </div>

              <!-- Assessment Duration -->
              <div>
                <Label for="assessmentDuration" class="text-gray-300">Assessment Duration (minutes)</Label>
                <div class="flex items-center">
                  <Input
                    type="number"
                    id="assessmentDuration"
                    name="assessmentDuration"
                    v-model="assessmentDuration"
                    min="5"
                    max="180"
                    autocomplete="off"
                    class="w-32"
                    required
                  />
                </div>
                <div class="mt-1 text-xs text-gray-400">
                  Set the time limit for completing this assessment
                </div>
              </div>

              <!-- Question Selection Mode -->
              <div>
                <Label class="text-gray-300">Question Selection Mode</Label>
                <div class="flex space-x-4">
                  <div class="flex items-center">
                    <input
                      type="radio"
                      id="dynamicMode"
                      name="questionSelectionMode"
                      value="dynamic"
                      v-model="questionSelectionMode"
                      class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800"
                    />
                    <Label for="dynamicMode" class="ml-2 text-sm text-gray-300">
                      Dynamic
                      <span class="block text-xs text-gray-400">Questions randomly selected for each session</span>
                    </Label>
                  </div>
                  <div class="flex items-center">
                    <input
                      type="radio"
                      id="fixedMode"
                      name="questionSelectionMode"
                      value="fixed"
                      v-model="questionSelectionMode"
                      class="h-4 w-4 text-cyan-500 focus:ring-cyan-500 border-gray-700 bg-gray-800"
                    />
                    <Label for="fixedMode" class="ml-2 text-sm text-gray-300">
                      Fixed
                      <span class="block text-xs text-gray-400">Same questions for all sessions</span>
                    </Label>
                  </div>
                </div>
                <div class="mt-2 text-xs text-indigo-300 bg-indigo-900/20 p-2 rounded border border-indigo-800/30">
                  <p><strong>Note:</strong></p>
                  <ul class="list-disc list-inside mt-1 space-y-1">
                    <li><strong>Dynamic mode:</strong> Questions are randomly selected from the skill's question pool for each session.</li>
                    <li><strong>Fixed mode:</strong> After creating the assessment, you'll need to go to "Add Fixed Questions" to select specific questions that will be used for all sessions.</li>
                  </ul>
                </div>
              </div>

              <!-- Loading indicator -->
              <div v-if="isLoading" class="flex justify-center items-center py-4">
                <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-cyan-500"></div>
                <span class="ml-3 text-gray-300">Creating assessment...</span>
              </div>

              <!-- Error/Success message -->
              <Alert v-if="message" :variant="isSuccess ? 'success' : 'error'">
                <AlertDescription>{{ message }}</AlertDescription>
              </Alert>

              <!-- Assessment details after creation -->
              <div v-if="createdAssessmentDetails" class="mt-4 bg-gray-800/70 border border-gray-700 rounded-lg p-4">
                <h3 class="text-cyan-400 font-medium mb-2">Assessment Details:</h3>
                <div class="space-y-2 text-sm text-gray-300">
                  <p><span class="text-gray-400">Assessment Name:</span> {{ createdAssessmentDetails.assessment_base_name }}</p>
                  <p><span class="text-gray-400">Assessment Description:</span> {{ createdAssessmentDetails.assessment_description || assessmentDescription }}</p>
                  <p><span class="text-gray-400">Assessment ID:</span> {{ createdAssessmentDetails.assessment_id }}</p>
                  <p><span class="text-gray-400">Duration:</span> {{ createdAssessmentDetails.duration || assessmentDuration }} minutes</p>
                  <p><span class="text-gray-400">Question Selection Mode:</span>
                    <span class="capitalize">{{ createdAssessmentDetails.question_selection_mode }}</span>
                    <span v-if="createdAssessmentDetails.question_selection_mode === 'fixed'" class="ml-2 text-xs text-indigo-300">
                      (Add fixed questions below)
                    </span>
                  </p>
                  <div v-if="createdAssessmentDetails.skill_ids && createdAssessmentDetails.skill_ids.length > 0">
                    <p class="text-gray-400 mb-1">Skills:</p>
                    <div class="flex flex-wrap gap-2 mb-2">
                      <span
                        v-for="skillId in createdAssessmentDetails.skill_ids"
                        :key="skillId"
                        class="inline-block bg-cyan-900/30 text-cyan-200 text-xs px-2 py-1 rounded-lg"
                      >
                        {{ getSkillName(skillId) }}
                      </span>
                    </div>
                  </div>
                  <p v-if="createdAssessmentDetails.total_questions_available">
                    <span class="text-gray-400">Available Questions:</span> {{ createdAssessmentDetails.total_questions_available }}
                  </p>
                  <div class="mt-4" v-if="questionSelectionMode === 'dynamic'">
                    <p class="text-cyan-400 font-medium">Next Steps:</p>
                    <p class="text-gray-300">
                      Use the "Generate Sessions" menu to create session codes for this assessment.
                    </p>
                    <div class="mt-2">
                      <Button
                        @click="navigateTo('/generate-sessions')"
                        variant="generalAction"
                        size="skillButton"
                      >
                        Go to Generate Sessions
                      </Button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Add Fixed Questions Section (shown when fixed mode is selected) -->
              <div v-if="questionSelectionMode === 'fixed'" class="mt-6 bg-indigo-900/20 border border-indigo-700 rounded-lg p-6">
                <h3 class="text-indigo-400 font-medium mb-4">Add Fixed Questions</h3>

                <!-- Instructions -->
                <div class="mb-4 bg-blue-900/20 border border-blue-700 rounded-lg p-3">
                  <p class="text-blue-300 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Select your questions below. When you click "Create Assessment", both the assessment and the selected questions will be created together.
                  </p>
                </div>

                <!-- Manual Question Number Input -->
                <div class="mb-6">
                  <Label class="text-gray-300 mb-2 block">Question Distribution</Label>
                  <div class="grid grid-cols-3 gap-4">
                    <div>
                      <Label class="text-xs text-green-400">Easy (min: 6)</Label>
                      <Input
                        type="number"
                        v-model.number="manualQuestionCounts.easy"
                        min="6"
                        autocomplete="off"
                        placeholder="6"
                      />
                    </div>
                    <div>
                      <Label class="text-xs text-yellow-400">Intermediate (min: 6)</Label>
                      <Input
                        type="number"
                        v-model.number="manualQuestionCounts.intermediate"
                        min="6"
                        autocomplete="off"
                        placeholder="6"
                      />
                    </div>
                    <div>
                      <Label class="text-xs text-red-400">Advanced (min: 8)</Label>
                      <Input
                        type="number"
                        v-model.number="manualQuestionCounts.advanced"
                        min="8"
                        autocomplete="off"
                        placeholder="8"
                      />
                    </div>
                  </div>
                  <div class="mt-2 text-xs text-gray-400">
                    Specify the number of questions for each difficulty level. You can add more than the minimum requirements.
                  </div>
                </div>

                <!-- Selected Questions Summary -->
                <div class="mb-4">
                  <div class="flex justify-between items-center mb-1">
                    <Label class="text-gray-300">Selected Questions</Label>
                    <span class="text-xs text-indigo-300">
                      {{ getSelectedQuestionCount() }} selected
                    </span>
                  </div>
                  <div class="bg-gray-800/50 border border-gray-700 rounded-lg p-3 min-h-[60px]">
                    <div v-if="getSelectedQuestionCount() === 0" class="text-gray-500 text-sm italic">
                      No questions selected. Select questions from the list below.
                    </div>
                    <div v-else class="flex flex-wrap gap-2">
                      <div
                        v-for="id in getSelectedQuestionIds()"
                        :key="id"
                        class="flex items-center bg-indigo-900/40 text-indigo-200 text-xs px-2 py-1 rounded"
                      >
                        <span>ID: {{ id }}</span>
                        <Button
                          @click="removeQuestionFromSelection(id)"
                          variant="ghost"
                          size="xs"
                          class="ml-1 text-indigo-300 hover:text-white hover:bg-indigo-900/30 rounded px-1"
                        >
                          &times;
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- No Questions Available Message -->
                <div v-if="selectedSkillIds.length > 0 && availableQuestions.length === 0 && !isLoadingQuestions" class="mb-4 bg-yellow-900/20 border border-yellow-700 rounded-lg p-3">
                  <p class="text-yellow-300 text-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    No questions available for the selected skills. Please select different skills or add questions to these skills first.
                  </p>
                </div>

                <!-- Loading Questions -->
                <div v-if="isLoadingQuestions" class="mb-4 flex justify-center items-center py-4">
                  <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-indigo-500"></div>
                  <span class="ml-3 text-gray-300">Loading questions...</span>
                </div>

                <!-- Available Questions -->
                <div v-if="availableQuestions.length > 0" class="mb-4">
                  <div class="flex justify-between items-center mb-2">
                    <div class="flex items-center space-x-3">
                      <h4 class="text-sm font-medium text-gray-300">Available Questions</h4>
                      <!-- Random Selection Button -->
                      <Button
                        @click.prevent="selectRandomQuestions"
                        variant="generalAction"
                        size="skillButton"
                        :disabled="availableQuestions.length === 0"
                        title="Randomly select questions based on assessment requirements"
                      >
                        <span class="flex items-center">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          Random Select
                        </span>
                      </Button>
                    </div>
                  </div>

                  <!-- Search and Filter -->
                  <div class="flex space-x-4 mb-3">
                    <div class="flex-1">
                      <Input
                        v-model="searchQuery"
                        placeholder="Search questions..."
                        class="text-sm"
                      />
                    </div>
                    <div class="w-40">
                      <select
                        v-model="difficultyFilter"
                        class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500"
                      >
                        <option value="all">All Difficulties</option>
                        <option value="easy">Easy</option>
                        <option value="intermediate">Intermediate</option>
                        <option value="advanced">Advanced</option>
                      </select>
                    </div>
                  </div>

                  <div class="bg-gray-800/50 border border-gray-700 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <div v-if="filteredQuestions.length === 0" class="text-center py-4 text-gray-400">
                      No questions match your filters.
                    </div>
                    <div v-else class="space-y-4">
                      <div v-for="question in filteredQuestions" :key="question.que_id"
                           class="p-3 border border-gray-700 rounded-lg hover:border-indigo-500/50 transition-all"
                           :class="{ 'border-indigo-500/50 bg-indigo-900/10': isQuestionSelected(question.que_id) }">
                        <div class="flex justify-between">
                          <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs rounded-full"
                                  :class="{
                                    'bg-green-900/50 text-green-400': question.level === 'easy',
                                    'bg-yellow-900/50 text-yellow-400': question.level === 'intermediate',
                                    'bg-red-900/50 text-red-400': question.level === 'advanced'
                                  }">
                              {{ question.level.charAt(0).toUpperCase() + question.level.slice(1) }}
                            </span>
                            <span class="text-gray-400 text-sm">ID: {{ question.que_id }}</span>
                            <span class="text-gray-400 text-sm">{{ question.skill_name }}</span>
                          </div>
                          <Button
                            @click="addQuestionToSelection(question.que_id)"
                            variant="generalAction"
                            size="skillButton"
                            :class="isQuestionSelected(question.que_id)
                              ? 'bg-indigo-700/70 text-indigo-200 hover:bg-indigo-600/70'
                              : 'bg-indigo-900/50 text-indigo-400 hover:bg-indigo-800/50'"
                          >
                            {{ isQuestionSelected(question.que_id) ? 'Selected' : 'Select' }}
                          </Button>
                        </div>
                        <div class="mt-2 text-white text-sm">{{ question.question }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Selected Questions Summary for Fixed Mode -->
                <div class="mt-4">
                  <div class="text-sm text-gray-400">
                    <span v-if="getSelectedQuestionCount() > 0">
                      {{ getSelectedQuestionCount() }} questions selected
                      <span v-if="getTotalManualQuestions() > 0"
                            :class="{
                              'text-green-400': getSelectedQuestionCount() === getTotalManualQuestions(),
                              'text-yellow-400': getSelectedQuestionCount() < getTotalManualQuestions(),
                              'text-red-400': getSelectedQuestionCount() > getTotalManualQuestions()
                            }">
                        ({{ getTotalManualQuestions() }} required)
                      </span>
                    </span>
                    <span v-else class="text-yellow-400">
                      Please select questions before creating the assessment
                    </span>
                  </div>
                </div>
              </div>

              <!-- Submit button -->
              <div class="flex justify-end">
                <Button
                  type="submit"
                  variant="assessmentGenerate"
                  size="skillButton"
                  :disabled="isLoading || isLoadingQuestions"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    {{ questionSelectionMode === 'fixed' ? 'Create Assessment with Selected Questions' : 'Create Assessment' }}
                  </span>
                </Button>
              </div>
            </form>
    </FormCard>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { safeAddEventListener } from '@/utils/domHelpers';
import { HerbitProfessionalLayout } from '@/components/layout';
import { FormCard } from '@/components/ui/form-card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Form data
const assessmentName = ref('');
const assessmentDescription = ref('');
const selectedSkillIds = ref([]); // Changed to array for multiple selection
const questionSelectionMode = ref('dynamic'); // Default to dynamic mode
const assessmentDuration = ref(30); // Default duration in minutes
const skills = ref([]);
const isLoading = ref(false);
const createdAssessmentDetails = ref(null);
const showSkillDropdown = ref(false); // For dropdown toggle

// Fixed questions data
const availableQuestions = ref([]);
const questionIds = ref('');
const isLoadingQuestions = ref(false);
const manualQuestionCounts = ref({
  easy: 6,
  intermediate: 6,
  advanced: 8
});
const searchQuery = ref('');
const difficultyFilter = ref('all');

// Helper functions for skill management
const getSkillName = (skillId) => {
  const skill = skills.value.find(s => s.id === skillId);
  return skill ? skill.name : `Skill ${skillId}`;
};

const removeSkill = (skillId) => {
  selectedSkillIds.value = selectedSkillIds.value.filter(id => id !== skillId);
  // Fetch questions again if in fixed mode
  if (questionSelectionMode.value === 'fixed' && selectedSkillIds.value.length > 0) {
    fetchQuestionsForSkills();
  } else if (selectedSkillIds.value.length === 0) {
    availableQuestions.value = [];
  }
};

const toggleSkillDropdown = () => {
  showSkillDropdown.value = !showSkillDropdown.value;
};

const onSkillSelectionChange = () => {
  // The watcher will automatically handle fetching questions
  // This function is here for any additional logic if needed
};

// Close dropdown when clicking outside
const closeDropdownOnOutsideClick = (event) => {
  if (showSkillDropdown.value && event.target && event.target.closest) {
    const container = event.target.closest('.skill-dropdown-container');
    if (!container) {
      showSkillDropdown.value = false;
    }
  }
};

// Fetch skills from API
const fetchSkills = async () => {
  try {
    isLoading.value = true;
    const response = await api.admin.getSkills();
    skills.value = response.data;
  } catch (error) {
    logError(error, 'fetchSkills');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch skills'));
  } finally {
    isLoading.value = false;
  }
};

// Create assessment via API
const createAssessment = async () => {
  // Enhanced validation
  if (!assessmentName.value || !assessmentDescription.value || selectedSkillIds.value.length === 0) {
    setErrorMessage('Please fill in all required fields and select at least one skill');
    return;
  }

  // Additional validation
  if (assessmentName.value.trim().length < 3) {
    setErrorMessage('Assessment name must be at least 3 characters long');
    return;
  }

  if (assessmentDescription.value.trim().length < 20) {
    setErrorMessage('Assessment description must be at least 20 characters long');
    return;
  }

  if (selectedSkillIds.value.length > 10) {
    setErrorMessage('Please select no more than 10 skills for an assessment');
    return;
  }

  // Additional validation for fixed mode
  if (questionSelectionMode.value === 'fixed') {
    const selectedQuestionCount = getSelectedQuestionCount();
    const requiredQuestionCount = getTotalManualQuestions();

    if (selectedQuestionCount === 0) {
      setErrorMessage('Please select questions for your fixed assessment');
      return;
    }

    if (selectedQuestionCount !== requiredQuestionCount) {
      setErrorMessage(`You have selected ${selectedQuestionCount} questions but specified ${requiredQuestionCount} questions in the distribution. Please adjust either your selection or the distribution to match.`);
      return;
    }

    // Validate minimum requirements
    if (manualQuestionCounts.value.easy < 6) {
      setErrorMessage('Easy questions must be at least 6');
      return;
    }
    if (manualQuestionCounts.value.intermediate < 6) {
      setErrorMessage('Intermediate questions must be at least 6');
      return;
    }
    if (manualQuestionCounts.value.advanced < 8) {
      setErrorMessage('Advanced questions must be at least 8');
      return;
    }
  }

  isLoading.value = true;
  clearMessage();
  createdAssessmentDetails.value = null;

  try {
    // Get the primary skill for the topic (using the first selected skill)
    const primarySkillId = selectedSkillIds.value[0];
    const primarySkill = skills.value.find(skill => skill.id === primarySkillId);

    if (!primarySkill) {
      throw new Error('Selected skill not found');
    }

    // Get current username (in a real app, this would come from auth)
    const username = localStorage.getItem('username') || 'admin_user';

    // Call the API to create the quiz/assessment
    const response = await api.admin.createAssessment({
      quiz_name: assessmentName.value,
      topic: assessmentDescription.value, // Use the new description field instead of skill description
      user_id: username,
      skill_ids: selectedSkillIds.value.map(id => parseInt(id)), // Convert all selected skill IDs to integers
      question_selection_mode: questionSelectionMode.value,
      duration: parseInt(assessmentDuration.value), // Add duration in minutes
      create_single_assessment: true // Create only one assessment instead of mock and final
    });

    // Extract data from response
    const responseData = response.data;

    // Store the response details for display
    createdAssessmentDetails.value = responseData;

    // If fixed mode and questions are selected, automatically add them
    if (questionSelectionMode.value === 'fixed') {
      await fetchAssessmentQuestions();

      // If questions were pre-selected, add them automatically
      if (getSelectedQuestionCount() > 0) {
        try {
          await addFixedQuestions();
          setSuccessMessage(`Successfully created "${assessmentName.value}" assessment with ${getSelectedQuestionCount()} fixed questions!`);
        } catch (error) {
          // Error is already handled in addFixedQuestions function
          console.warn('Failed to auto-add selected questions:', error);
          setSuccessMessage(`Assessment "${assessmentName.value}" created, but failed to add questions. Please add them manually.`);
        }
      } else {
        setSuccessMessage(`Successfully created "${assessmentName.value}" assessment!`);
      }
    } else {
      setSuccessMessage(`Successfully created "${assessmentName.value}" assessment!`);
    }

    // Reset form after success (but keep selected questions for fixed mode)
    assessmentName.value = '';
    selectedSkillIds.value = [];
    assessmentDuration.value = 30; // Reset to default duration

  } catch (error) {
    logError(error, 'createAssessment');
    setErrorMessage(getErrorMessage(error, 'An unexpected error occurred while creating the assessment'));
    createdAssessmentDetails.value = null;
  } finally {
    isLoading.value = false;
  }
};

// Fixed Questions Functions
const fetchAssessmentQuestions = async () => {
  if (!createdAssessmentDetails.value?.assessment_id) {
    return;
  }

  isLoadingQuestions.value = true;
  try {
    const questionsResponse = await api.admin.getAssessmentQuestions(createdAssessmentDetails.value.assessment_id);
    const questionData = questionsResponse.data;
    availableQuestions.value = questionData.questions || [];
  } catch (error) {
    logError(error, 'fetchAssessmentQuestions');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch questions for this assessment'));
  } finally {
    isLoadingQuestions.value = false;
  }
};

// Fetch questions for selected skills (used when in fixed mode)
const fetchQuestionsForSkills = async () => {
  if (selectedSkillIds.value.length === 0) {
    availableQuestions.value = [];
    return;
  }

  isLoadingQuestions.value = true;
  try {
    // Fetch questions for all selected skills
    const allQuestions = [];
    for (const skillId of selectedSkillIds.value) {
      try {
        const response = await api.admin.getSkillQuestions(skillId);

        if (response.data && response.data.questions) {
          // Add skill_name to each question for display purposes
          const questionsWithSkillName = response.data.questions.map(question => ({
            ...question,
            skill_name: response.data.skill_name
          }));
          allQuestions.push(...questionsWithSkillName);
        }
      } catch (error) {
        console.warn(`Failed to fetch questions for skill ${skillId}:`, error);
      }
    }

    // Remove duplicates based on question ID
    const uniqueQuestions = allQuestions.filter((question, index, self) =>
      index === self.findIndex(q => q.que_id === question.que_id)
    );

    availableQuestions.value = uniqueQuestions;
  } catch (error) {
    logError(error, 'fetchQuestionsForSkills');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch questions for selected skills'));
  } finally {
    isLoadingQuestions.value = false;
  }
};

// Computed property for filtered questions
const filteredQuestions = computed(() => {
  let filtered = availableQuestions.value;

  // Filter by search query
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(q =>
      q.question.toLowerCase().includes(query) ||
      q.skill_name.toLowerCase().includes(query)
    );
  }

  // Filter by difficulty
  if (difficultyFilter.value !== 'all') {
    filtered = filtered.filter(q => q.level === difficultyFilter.value);
  }

  return filtered;
});

// Helper methods for question selection
const getSelectedQuestionIds = () => {
  return questionIds.value.split(',')
    .map(id => id.trim())
    .filter(id => id)
    .map(id => parseInt(id));
};

const getSelectedQuestionCount = () => {
  return getSelectedQuestionIds().length;
};

const getTotalManualQuestions = () => {
  return manualQuestionCounts.value.easy + manualQuestionCounts.value.intermediate + manualQuestionCounts.value.advanced;
};

const isQuestionSelected = (questionId) => {
  return getSelectedQuestionIds().includes(questionId);
};

const addQuestionToSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();

  if (selectedIds.includes(questionId)) {
    // Remove the question if already selected
    removeQuestionFromSelection(questionId);
  } else {
    // Add the question if not already selected
    selectedIds.push(questionId);
    questionIds.value = selectedIds.join(', ');
  }
};

const removeQuestionFromSelection = (questionId) => {
  const selectedIds = getSelectedQuestionIds();
  const updatedIds = selectedIds.filter(id => id !== questionId);
  questionIds.value = updatedIds.join(', ');
};

// Randomly select questions based on assessment requirements
const selectRandomQuestions = () => {
  // Validate manual question counts meet minimum requirements
  if (manualQuestionCounts.value.easy < 6) {
    setErrorMessage('Easy questions must be at least 6. You can specify more than 6 if needed.');
    return;
  }
  if (manualQuestionCounts.value.intermediate < 6) {
    setErrorMessage('Intermediate questions must be at least 6. You can specify more than 6 if needed.');
    return;
  }
  if (manualQuestionCounts.value.advanced < 8) {
    setErrorMessage('Advanced questions must be at least 8. You can specify more than 8 if needed.');
    return;
  }

  // Group questions by difficulty
  const easyQuestions = availableQuestions.value.filter(q => q.level === 'easy');
  const intermediateQuestions = availableQuestions.value.filter(q => q.level === 'intermediate');
  const advancedQuestions = availableQuestions.value.filter(q => q.level === 'advanced');

  // Check if we have enough questions for each difficulty
  const easyCount = manualQuestionCounts.value.easy;
  const intermediateCount = manualQuestionCounts.value.intermediate;
  const advancedCount = manualQuestionCounts.value.advanced;

  if (easyQuestions.length < easyCount) {
    setErrorMessage(`Not enough easy questions available. Required: ${easyCount}, Available: ${easyQuestions.length}`);
    return;
  }
  if (intermediateQuestions.length < intermediateCount) {
    setErrorMessage(`Not enough intermediate questions available. Required: ${intermediateCount}, Available: ${intermediateQuestions.length}`);
    return;
  }
  if (advancedQuestions.length < advancedCount) {
    setErrorMessage(`Not enough advanced questions available. Required: ${advancedCount}, Available: ${advancedQuestions.length}`);
    return;
  }

  // Shuffle array function
  const shuffleArray = (array) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // Shuffle and select questions by difficulty
  const selectedEasy = shuffleArray([...easyQuestions]).slice(0, easyCount);
  const selectedIntermediate = shuffleArray([...intermediateQuestions]).slice(0, intermediateCount);
  const selectedAdvanced = shuffleArray([...advancedQuestions]).slice(0, advancedCount);

  // Combine all selected questions
  const selectedQuestions = [...selectedEasy, ...selectedIntermediate, ...selectedAdvanced];

  // Update the selection
  questionIds.value = selectedQuestions.map(q => q.que_id).join(', ');

  // Show success message
  setSuccessMessage(`Randomly selected ${selectedQuestions.length} questions based on assessment requirements.`);
};

// Add fixed questions via API
const addFixedQuestions = async () => {
  if (!createdAssessmentDetails.value?.assessment_id) {
    setErrorMessage('Please create the assessment first before adding fixed questions');
    return;
  }

  const selectedIds = getSelectedQuestionIds();
  if (selectedIds.length === 0) {
    setErrorMessage('Please select at least one question');
    return;
  }

  // Validate minimum requirements
  if (manualQuestionCounts.value.easy < 6) {
    setErrorMessage('Easy questions must be at least 6. You can add more than 6 if needed.');
    return;
  }
  if (manualQuestionCounts.value.intermediate < 6) {
    setErrorMessage('Intermediate questions must be at least 6. You can add more than 6 if needed.');
    return;
  }
  if (manualQuestionCounts.value.advanced < 8) {
    setErrorMessage('Advanced questions must be at least 8. You can add more than 8 if needed.');
    return;
  }

  // Check if selected questions match the manual counts
  const totalSelected = selectedIds.length;
  const totalRequired = getTotalManualQuestions();

  if (totalSelected !== totalRequired) {
    setErrorMessage(`You have selected ${totalSelected} questions but specified ${totalRequired} questions in the manual entry. Please adjust either your selection or the manual counts to match.`);
    return;
  }

  isLoadingQuestions.value = true;
  clearMessage();

  try {
    // Call the API to add fixed questions
    await api.admin.addFinalQuestions({
      assessment_id: parseInt(createdAssessmentDetails.value.assessment_id),
      question_ids: selectedIds,
      quiz_name: createdAssessmentDetails.value.assessment_base_name || 'Assessment',
      question_distribution: {
        easy: manualQuestionCounts.value.easy,
        intermediate: manualQuestionCounts.value.intermediate,
        advanced: manualQuestionCounts.value.advanced,
           total: getTotalManualQuestions()
      }
    });

    // Don't show success message here - let the calling function handle it

    // Clear the selection after success
    questionIds.value = '';

  } catch (error) {
    logError(error, 'addFixedQuestions');
    setErrorMessage(getErrorMessage(error, 'An unexpected error occurred while assigning questions'));
  } finally {
    isLoadingQuestions.value = false;
  }
};

// Watchers to automatically fetch questions when skills or mode changes
watch([selectedSkillIds, questionSelectionMode], () => {
  if (questionSelectionMode.value === 'fixed' && selectedSkillIds.value.length > 0) {
    fetchQuestionsForSkills();
  } else {
    availableQuestions.value = [];
    questionIds.value = '';
  }
}, { deep: true });

// Store cleanup function for event listener
let cleanupEventListener = null;

onMounted(() => {
  fetchSkills();

  // Add event listener for closing dropdown when clicking outside
  cleanupEventListener = safeAddEventListener(document, 'click', closeDropdownOnOutsideClick);
});

// Clean up event listener when component is unmounted
onUnmounted(() => {
  if (cleanupEventListener) {
    cleanupEventListener();
  }
});
</script>


